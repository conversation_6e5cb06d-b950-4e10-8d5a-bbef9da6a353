Experiment name: scripts-SpatialGym
Namespace(yaml_path='/home/<USER>/Work/spatial/VAGEN-new/VAGEN/scripts/SpatialGym/env_config.yaml', force_gen=True, train_path='data/scripts-SpatialGym/train.parquet', test_path='data/scripts-SpatialGym/test.parquet', seed=42)
Using 10 trian seeds generated by spatial config's generate_seeds method
Using 10 test seeds generated by spatial config's generate_seeds method

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 4218.35 examples/s]

Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating parquet from Arrow format: 100%|██████████| 1/1 [00:00<00:00, 3026.19ba/s]
Train dataset with 10 instances saved to data/scripts-SpatialGym/train.parquet

Map:   0%|          | 0/10 [00:00<?, ? examples/s]
Map: 100%|██████████| 10/10 [00:00<00:00, 4918.27 examples/s]

Creating parquet from Arrow format:   0%|          | 0/1 [00:00<?, ?ba/s]
Creating parquet from Arrow format: 100%|██████████| 1/1 [00:00<00:00, 5236.33ba/s]
Test dataset with 10 instances saved to data/scripts-SpatialGym/test.parquet

Generating train split: 0 examples [00:00, ? examples/s]
Generating train split: 10 examples [00:00, 4441.24 examples/s]

Generating test split: 0 examples [00:00, ? examples/s]
Generating test split: 10 examples [00:00, 7426.18 examples/s]
{'data_source': 'spatial', 'prompt': [{'content': '', 'role': 'user'}], 'extra_info': {'env_config': {'eval_tasks': [{'task_kwargs': {'turn_direction': 'clockwise'}, 'task_type': 'rot'}], 'exp_type': 'passive'}, 'env_name': 'spatial', 'seed': 0, 'split': 'train'}}
SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot)
{'data_source': 'spatial', 'prompt': [{'content': '', 'role': 'user'}], 'extra_info': {'env_config': {'eval_tasks': [{'task_kwargs': {'turn_direction': 'clockwise'}, 'task_type': 'rot'}], 'exp_type': 'passive'}, 'env_name': 'spatial', 'seed': 1, 'split': 'train'}}
SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot)
{'data_source': 'spatial', 'prompt': [{'content': '', 'role': 'user'}], 'extra_info': {'env_config': {'eval_tasks': [{'task_kwargs': {'turn_direction': 'clockwise'}, 'task_type': 'rot'}], 'exp_type': 'passive'}, 'env_name': 'spatial', 'seed': 0, 'split': 'train'}}
SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot)
{'data_source': 'spatial', 'prompt': [{'content': '', 'role': 'user'}], 'extra_info': {'env_config': {'eval_tasks': [{'task_kwargs': {'turn_direction': 'clockwise'}, 'task_type': 'rot'}], 'exp_type': 'passive'}, 'env_name': 'spatial', 'seed': 1, 'split': 'train'}}
SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot)
INFO 07-20 01:33:38 [__init__.py:239] Automatically detected platform cuda.
2025-07-20 01:33:39,373 - __main__ - INFO - Starting inference pipeline
2025-07-20 01:33:39,384 - __main__ - INFO - Loaded 10 environment configurations
2025-07-20 01:33:39,384 - __main__ - INFO - Running inference for model: gpt4o
wandb: Currently logged in as: pingyuezhang2029 (ragen-V) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /home/<USER>/Work/spatial/VAGEN-new/VAGEN/wandb/run-20250720_013339-8c6dvib3
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run gpt4o_spatial_gym_inference
wandb: ⭐️ View project at https://wandb.ai/ragen-V/vagen-debug
wandb: 🚀 View run at https://wandb.ai/ragen-V/vagen-debug/runs/8c6dvib3
2025-07-20 01:33:40,058 - vagen.inference.model_interface.factory_model - INFO - Creating model interface for provider 'openai' with model 'gpt-4o'
2025-07-20 01:33:40,069 - vagen.inference.model_interface.openai.model - INFO - Initialized OpenAI interface with model gpt-4o

Inference steps:   0%|          | 0/3 [00:00<?, ?it/s]2025-07-20 01:33:44,007 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:44,432 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:44,443 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:44,584 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:44,725 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:44,908 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:45,050 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:45,062 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:45,555 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-07-20 01:33:46,535 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"

Inference steps:  33%|███▎      | 1/3 [00:05<00:10,  5.09s/it]
Inference steps:  33%|███▎      | 1/3 [00:05<00:10,  5.09s/it]
wandb: uploading artifact run-8c6dvib3-table
wandb:                                                                                
wandb: 
wandb: Run history:
wandb:  val/done/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) ▁
wandb: val/score/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) ▁
wandb:  val/step/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) ▁
wandb: 
wandb: Run summary:
wandb:  val/done/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) 1
wandb: val/score/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) 0.1
wandb:  val/step/SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot) 1
wandb: 
wandb: 🚀 View run gpt4o_spatial_gym_inference at: https://wandb.ai/ragen-V/vagen-debug/runs/8c6dvib3
wandb: ⭐️ View project at: https://wandb.ai/ragen-V/vagen-debug
wandb: Synced 5 W&B file(s), 1 media file(s), 56 artifact file(s) and 0 other file(s)
wandb: Find logs at: ./wandb/run-20250720_013339-8c6dvib3/logs
2025-07-20 01:33:48,385 - __main__ - INFO - Inference pipeline completed
Step 1: 100.0% environments completed, 0 active, avg steps: 1.0, gen time: 5.089s
[SCORE DEBUG] env_id=test_0, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_1, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_2, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_3, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_4, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_5, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_6, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_7, steps=1, grounding=0, worldmodeling=0, total=1
[SCORE DEBUG] env_id=test_8, steps=0, grounding=0, worldmodeling=0, total=0
[SCORE DEBUG] env_id=test_9, steps=0, grounding=0, worldmodeling=0, total=0

===== Results for gpt4o =====
Total environments: 10
Successful: 0
Completed: 10

Metrics by Config ID:
  SpatialGymConfig(mode=vision,format=free_think,eval_tasks=rot):
    score: 0.1000
    done: 1.0000
    step: 1.0000
