site_name: VAGEN
site_description: VAGEN is a multi-turn reinforcement learning framework designed specifically for training VLM Agents. VAGEN leverages the TRICO algorithm to efficiently train VLMs for visual agentic tasks.
site_author: <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>

# Material theme settings
theme:
  name: material
  palette:
    primary: indigo
    accent: indigo
  features:
    - header.autohide
    - navigation.instant
  icon:
    repo: fontawesome/brands/github
  repo_url: https://github.com/RAGEN-AI/VAGEN/tree/main
  repo_name: RAGEN-AI/VAGEN


plugins:
  - search


# Left Navigation bar
nav:
  - Home: index.md
  - Quick Start:
      - Installation and Run Experiment: run-exp.md
  - Configurations:
      - General Configuration: configs/general-config.md
      - Algorithm Configuration: configs/algo-config.md
  - Environments:
      - Create Environment: envs/create-env.md
      - Create Service: envs/create-service.md
  - Experiments:
      - Reproduce Experiments: reproduce-exp.md

# Copyright footer
copyright: Copyright © 2025, VAGEN Team

# Additional settings
markdown_extensions:
  - admonition
  - codehilite
  - footnotes
  - toc:
      permalink: true
  - pymdownx.highlight
  - pymdownx.superfences