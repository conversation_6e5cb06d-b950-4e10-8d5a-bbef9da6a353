import numpy as np
from vagen.env.spatial.Base.tos_base import <PERSON><PERSON>anager, CognitiveMapManager, ActionSequence, Room
from vagen.env.spatial.Base.tos_base.managers.cognitive_map_manager import COGMAP_EXP_REQUIRED_INSTRUCTION, COGMAP_EVAL_REQUIRED_INSTRUCTION
from vagen.env.spatial.utils.generate_history import AutoExplore
from .prompts import *

class Prompter:
    """A class to generate prompts for the SpatialGym environment."""
    ACTIVE_INSTRUCTION = ACTIVE_INSTRUCTION
    ACTIVE_INSTRUCTION_SHORTER = ACTIVE_INSTRUCTION_SHORTER
    PASSIVE_INSTRUCTION = PASSIVE_INSTRUCTION
    EVALUATION_INSTRUCTION = EVALUATION_INSTRUCTION
    FORMAT_PROMPT = FORMAT_PROMPT
    TOPDOWN_PROMPT = TOPDOWN_PROMPT
    OBLIQUE_PROMPT = OBLIQUE_PROMPT
    COGMAP_EXP_REQUIRED_INSTRUCTION = COGMAP_EXP_REQUIRED_INSTRUCTION
    COGMAP_EVAL_REQUIRED_INSTRUCTION = COGMAP_EVAL_REQUIRED_INSTRUCTION

    def __init__(self, config, image_handler, np_random):
        self.config = config
        self.image_handler = image_handler
        self.np_random = np_random

    def _get_topdown_prompt(self, prompt_template: str, room: Room) -> str:
        obj_info = "Each object in the room is labeled with a numerical marker for easy identification."
        for idx, obj in enumerate(room.objects):
            obj_info += f"\nObject {idx + 1}: {obj.name}"
        return prompt_template.format(placeholder=self.config.image_placeholder, object_info=obj_info)

    def get_initial_observation_prompt(
            self, room: Room,
            eval_manager: EvaluationManager = None,
            cogmap_manager: CognitiveMapManager = None,
            **kwargs,
        ) -> dict:
        """
        Generates the initial observation prompt based on the exploration type.
        """
        room_desc = room.get_room_description()
        result = {}
        if self.config.prompt_config['topdown']:
            room_desc += self._get_topdown_prompt(TOPDOWN_PROMPT, room)
        if self.config.prompt_config['oblique']:
            room_desc += self._get_topdown_prompt(OBLIQUE_PROMPT, room)
        cogmap_instruction = cogmap_manager.get_cognitive_map_instruction() if cogmap_manager else ""
            
        if self.config.exp_type == 'active':
            exp_instructions = ActionSequence.get_usage_instructions() + f"\n\nYou have a maximum of {self.config.max_exp_steps} exploration steps."
            if self.config.prompt_config['type'] == 'default':
                active_instruction = self.ACTIVE_INSTRUCTION
            elif self.config.prompt_config['type'] == 'shorter':
                active_instruction = self.ACTIVE_INSTRUCTION_SHORTER
            obs_str = active_instruction.format(
                room_info=room_desc,
                exp_instructions=exp_instructions,
                cogmap_instruction=cogmap_instruction
            )
            obs_str += '\n' + self.COGMAP_EXP_REQUIRED_INSTRUCTION if self.config.prompt_config['cogmap'] else ""

            if self.config.prompt_config['topdown']:
                result['multi_modal_data'] = {self.config.image_placeholder: [self.image_handler.get_image('topdown')]}
        
        else:
            images = []
            if not self.config.prompt_config['topdown'] and not self.config.prompt_config['oblique']:
                exp_history_obs = AutoExplore(room, self.np_random, self.image_handler).gen_exp_history()
                exp_history_str = "## Exploration History\n" + exp_history_obs['obs_str']
                images.extend(exp_history_obs['multi_modal_data'][self.config.image_placeholder])
            elif self.config.prompt_config['topdown']:
                images.append(self.image_handler.get_image('topdown'))
            elif self.config.prompt_config['oblique']:
                images.append(self.image_handler.get_image('oblique'))
                
            obs_str = self.PASSIVE_INSTRUCTION.format(
                room_info=room_desc,
                exp_history=exp_history_str,
                cogmap_instruction=cogmap_instruction
            )
            obs_str += f"\n{self.get_evaluation_prompt(eval_manager)}"
            obs_str += '\n' + self.COGMAP_EVAL_REQUIRED_INSTRUCTION if self.config.prompt_config['cogmap'] else ""

            if images:
                result['multi_modal_data'] = {self.config.image_placeholder: images}

        result['obs_str'] = obs_str + "\n" + self.FORMAT_PROMPT
        return result
        
            

    def get_evaluation_prompt(self, eval_manager: EvaluationManager) -> str:
        """Generate the evaluation prompt."""
        eval_question = eval_manager.get_current_question()
        assert eval_question, "No question found after exploration phase"
        return self.EVALUATION_INSTRUCTION.format(eval_question=f"## Evaluation Question\n{eval_question}")
