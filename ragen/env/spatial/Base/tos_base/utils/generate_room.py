import numpy as np
from typing import Tuple, List
import random

def generate_rooms(n: int, level: int, main: int = None, seed: int = None, debug: bool = False) -> np.ndarray:
    """
    生成房间布局的函数

    Args:
        n: 网格大小 (n x n)
        level: 复杂度等级，level=0表示1个房间，level=1表示2个房间，以此类推
        main: 主房间大小，如果指定则第一个房间将是main×main大小
        seed: 随机种子

    Returns:
        n x n的numpy数组，其中：
        - 1到level+1: 房间ID
        - 0: 墙
        - -1: 不可通行区域（房间外）
        - 100: 南北向门
        - 101: 东西向门
    """
    if seed is not None:
        np.random.seed(seed)
        random.seed(seed)

    num_rooms = level + 1

    # 初始化网格，全部为不可通行区域
    grid = np.full((n, n), -1, dtype=int)

    if num_rooms == 1:
        return _generate_single_room(grid, n, main)

    # 多次尝试生成有效的房间布局
    max_attempts = 100
    for attempt in range(max_attempts):
        # 重置网格
        grid = np.full((n, n), -1, dtype=int)

        # 生成房间布局
        rooms = _generate_room_layout(n, num_rooms, main)
        if not rooms or len(rooms) != num_rooms:
            continue

        # 生成房间连接（树状结构，无环）
        connections = _generate_tree_connections(rooms)

        # 在网格中放置房间
        for i, room in enumerate(rooms):
            room_id = i + 1
            x1, y1, x2, y2 = room
            grid[y1:y2+1, x1:x2+1] = room_id

        # 添加墙壁
        _add_walls_around_rooms(grid, rooms)

        # 添加门
        doors_added = _add_doors_between_rooms(grid, rooms, connections)

        # 检查是否成功添加所有门且连通
        if len(doors_added) == len(connections) and _verify_connectivity(grid, num_rooms):
            # 最后检查：确保没有房间到达边界
            if _verify_no_rooms_at_boundary(grid, num_rooms):
                return grid

    # 如果多次尝试失败，返回单个房间
    return _generate_single_room(grid, n, main)

def _generate_single_room(grid: np.ndarray, n: int, main: int = None) -> np.ndarray:
    """生成单个房间，占据中心区域"""
    # 计算房间大小，确保周围有墙的空间
    if main is not None:
        # 如果指定了main参数，使用main×main作为房间大小
        room_size = main
        if room_size > n - 4:  # 确保房间不会太大
            room_size = n - 4
        if room_size < 4:  # 确保房间不会太小
            room_size = 4
    else:
        # 默认房间大小计算
        min_room_size = 4
        max_room_size = n - 4  # 留出墙的空间
        room_size = max(min_room_size, max_room_size)

    # 计算房间位置（居中）
    start = (n - room_size) // 2
    end = start + room_size

    # 确保房间不会到达边界，周围必须有墙的空间
    if end >= n - 1:  # 至少留出1格给墙
        end = n - 2
        start = end - room_size + 1
    if start < 1:  # 至少留出1格给墙
        start = 1
        end = start + room_size - 1

    # 放置房间
    grid[start:end+1, start:end+1] = 1

    # 添加周围的墙
    wall_start_x = max(0, start - 1)
    wall_end_x = min(n - 1, end + 1)
    wall_start_y = max(0, start - 1)
    wall_end_y = min(n - 1, end + 1)

    # 先设置墙的区域
    grid[wall_start_y:wall_end_y+1, wall_start_x:wall_end_x+1] = 0
    # 再设置房间区域
    grid[start:end+1, start:end+1] = 1

    return grid

def _generate_room_layout(n: int, num_rooms: int, main: int = None) -> List[Tuple[int, int, int, int]]:
    """生成房间布局，返回房间坐标列表 (x1, y1, x2, y2)"""
    rooms = []

    # 根据房间数量调整房间大小，增加差异化
    if num_rooms == 1:
        # 单个房间，可以较大
        min_size = max(4, n // 4)
        max_size = max(min_size, n - 4)
    else:
        # 多个房间，增加大小差异化
        min_size = 4
        # 根据网格大小和房间数量动态调整最大尺寸
        if n >= 20:
            max_size = max(min_size, min(10, n // max(2, num_rooms - 1)))
        else:
            max_size = max(min_size, min(8, n // (num_rooms + 1)))

    max_attempts = 1000

    for i in range(num_rooms):
        attempts = 0
        placed = False

        while attempts < max_attempts and not placed:
            # 为每个房间生成不同的大小，增加差异化
            if i == 0 and main is not None:
                # 第一个房间使用指定的main×main大小
                width = main
                height = main
                # 确保main房间不会太大（基于网格大小，而不是max_size）
                max_possible_size = n - 4  # 留出墙的空间
                if width > max_possible_size:
                    width = max_possible_size
                if height > max_possible_size:
                    height = max_possible_size
                # 确保main房间不会太小
                if width < min_size:
                    width = min_size
                if height < min_size:
                    height = min_size
            elif num_rooms > 1:
                # 为不同房间使用不同的大小偏好，增加更大的差异
                if i == 0:
                    # 第一个房间较大（如果没有指定main）
                    room_min = min_size
                    room_max = max_size
                elif i == 1:
                    # 第二个房间中等大小
                    room_min = min_size
                    room_max = max(min_size, max_size - random.randint(1, 2))
                elif i == 2:
                    # 第三个房间较小
                    room_min = min_size
                    room_max = max(min_size, max_size - random.randint(2, 3))
                else:
                    # 其他房间大小随机变化
                    size_variation = random.choice([0, 1, 2, 3])
                    room_min = min_size
                    room_max = max(min_size, max_size - size_variation)

                # 生成房间大小，宽度和高度可以不同
                width = random.randint(room_min, room_max)
                height = random.randint(room_min, room_max)

                # 增加长方形房间的概率，并且差异更大
                if random.random() < 0.6:  # 60%概率生成长方形
                    if random.random() < 0.5:
                        # 增加宽度
                        width = min(max_size, width + random.randint(1, 3))
                    else:
                        # 增加高度
                        height = min(max_size, height + random.randint(1, 3))
            else:
                # 单个房间的情况
                room_min = min_size
                room_max = max_size
                width = random.randint(room_min, room_max)
                height = random.randint(room_min, room_max)

            # 确保房间不到达边界，周围必须有墙的空间
            max_x = n - width - 1  # 至少留出1格给墙
            max_y = n - height - 1  # 至少留出1格给墙

            if max_x < 1 or max_y < 1:
                # 空间不够，缩小房间
                width = max(min_size, n - 2)  # 两边各留1格
                height = max(min_size, n - 2)  # 两边各留1格
                max_x = n - width - 1
                max_y = n - height - 1

                if max_x < 1 or max_y < 1:
                    break

            # 随机生成房间位置（从1开始，确保周围有墙的空间）
            x1 = random.randint(1, max_x)
            y1 = random.randint(1, max_y)
            x2 = x1 + width - 1
            y2 = y1 + height - 1

            new_room = (x1, y1, x2, y2)

            # 检查是否与现有房间重叠
            if not _rooms_overlap_with_walls(new_room, rooms):
                rooms.append(new_room)
                placed = True

            attempts += 1

        if not placed:
            # 尝试强制放置一个更小的房间
            forced_room = _force_place_small_room(n, rooms)
            if forced_room:
                rooms.append(forced_room)
            else:
                # 如果连强制放置都失败，停止生成更多房间
                break

    return rooms

def _rooms_overlap_with_walls(new_room: Tuple[int, int, int, int],
                             existing_rooms: List[Tuple[int, int, int, int]]) -> bool:
    """检查房间是否重叠（包括墙壁和门的空间）"""
    x1, y1, x2, y2 = new_room

    for ex1, ey1, ex2, ey2 in existing_rooms:
        # 检查是否可以相邻（允许共享墙壁）
        # 如果房间可以相邻，则只需要1格的间距用于墙
        wall_margin = 1
        if not (x2 + wall_margin < ex1 or x1 > ex2 + wall_margin or
                y2 + wall_margin < ey1 or y1 > ey2 + wall_margin):
            return True
    return False

def _force_place_small_room(n: int, existing_rooms: List[Tuple[int, int, int, int]]) -> Tuple[int, int, int, int]:
    """强制放置一个小房间"""
    min_size = 4
    max_size = min(6, n // 4)

    for size in range(min_size, max_size + 1):
        for x in range(1, n - size - 1):  # 确保房间不到达边界
            for y in range(1, n - size - 1):  # 确保房间不到达边界
                room = (x, y, x + size - 1, y + size - 1)
                if not _rooms_overlap_with_walls(room, existing_rooms):
                    return room

    # 最后的备选方案：放置最小房间（4x4）
    if n >= 6:  # 确保有足够空间放置4x4房间加墙
        return (1, 1, 4, 4)
    return None

def _generate_tree_connections(rooms: List[Tuple[int, int, int, int]]) -> List[Tuple[int, int]]:
    """生成房间连接的树状结构（无环），使用最小生成树算法"""
    if len(rooms) <= 1:
        return []

    # 计算房间中心点
    centers = []
    for x1, y1, x2, y2 in rooms:
        centers.append(((x1 + x2) // 2, (y1 + y2) // 2))

    # 使用Prim算法生成最小生成树，确保无环
    visited = [False] * len(rooms)
    visited[0] = True  # 从第一个房间开始
    connections = []

    while len(connections) < len(rooms) - 1:
        min_dist = float('inf')
        best_edge = None

        # 找到已访问房间到未访问房间的最短连接
        for i in range(len(rooms)):
            if not visited[i]:
                continue
            for j in range(len(rooms)):
                if visited[j]:
                    continue

                # 计算曼哈顿距离
                cx1, cy1 = centers[i]
                cx2, cy2 = centers[j]
                dist = abs(cx1 - cx2) + abs(cy1 - cy2)

                if dist < min_dist:
                    min_dist = dist
                    best_edge = (i, j)

        if best_edge:
            i, j = best_edge
            visited[j] = True
            connections.append(best_edge)
        else:
            break  # 无法找到更多连接

    return connections

def _add_walls_around_rooms(grid: np.ndarray, rooms: List[Tuple[int, int, int, int]]):
    """为所有房间添加周围的墙壁，房间不会到达边界，所以周围总是有空间放墙"""
    n = grid.shape[0]

    for i, room in enumerate(rooms):
        x1, y1, x2, y2 = room
        current_room_id = i + 1

        # 由于房间不会到达边界，所以总是可以在周围添加墙
        # 上墙
        for x in range(max(0, x1-1), min(n, x2+2)):
            pos_value = grid[y1-1, x]
            # 在不可通行区域或其他房间边界设置墙
            if pos_value == -1 or (1 <= pos_value <= len(rooms) and pos_value != current_room_id):
                grid[y1-1, x] = 0

        # 下墙
        for x in range(max(0, x1-1), min(n, x2+2)):
            pos_value = grid[y2+1, x]
            if pos_value == -1 or (1 <= pos_value <= len(rooms) and pos_value != current_room_id):
                grid[y2+1, x] = 0

        # 左墙
        for y in range(max(0, y1-1), min(n, y2+2)):
            pos_value = grid[y, x1-1]
            if pos_value == -1 or (1 <= pos_value <= len(rooms) and pos_value != current_room_id):
                grid[y, x1-1] = 0

        # 右墙
        for y in range(max(0, y1-1), min(n, y2+2)):
            pos_value = grid[y, x2+1]
            if pos_value == -1 or (1 <= pos_value <= len(rooms) and pos_value != current_room_id):
                grid[y, x2+1] = 0

    # 第二遍：确保所有房间周围都有墙或门，不能直接与-1相邻
    _ensure_rooms_surrounded_by_walls_or_doors(grid, rooms)

def _ensure_rooms_surrounded_by_walls_or_doors(grid: np.ndarray, rooms: List[Tuple[int, int, int, int]]):
    """确保所有房间周围都有墙或门，房间不能直接与-1相邻"""
    n = grid.shape[0]

    for i, room in enumerate(rooms):
        x1, y1, x2, y2 = room
        current_room_id = i + 1

        # 检查房间的每个边界位置
        for y in range(y1, y2 + 1):
            for x in range(x1, x2 + 1):
                if grid[y, x] == current_room_id:
                    # 检查房间格子的四个方向
                    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 右、左、下、上

                    for dy, dx in directions:
                        ny, nx = y + dy, x + dx

                        # 如果相邻位置在网格内
                        if 0 <= ny < n and 0 <= nx < n:
                            neighbor_value = grid[ny, nx]

                            # 如果房间直接与-1相邻，需要在中间添加墙
                            if neighbor_value == -1:
                                grid[ny, nx] = 0  # 设置为墙

def _add_doors_between_rooms(grid: np.ndarray, rooms: List[Tuple[int, int, int, int]],
                            connections: List[Tuple[int, int]]) -> List[Tuple[int, int, int]]:
    """在连接的房间之间添加门，返回成功添加的门列表"""
    doors_added = []

    for room1_idx, room2_idx in connections:
        room1 = rooms[room1_idx]
        room2 = rooms[room2_idx]

        # 找到两个房间之间门的位置
        door_pos = _find_door_between_rooms(grid, room1, room2)
        if door_pos:
            x, y, door_type = door_pos
            grid[y, x] = door_type
            doors_added.append(door_pos)

    return doors_added

def _find_door_between_rooms(grid: np.ndarray, room1: Tuple[int, int, int, int],
                            room2: Tuple[int, int, int, int]) -> Tuple[int, int, int]:
    """找到两个房间之间门的位置"""
    x1_1, y1_1, x2_1, y2_1 = room1
    x1_2, y1_2, x2_2, y2_2 = room2

    # 检查水平相邻（东西向门）
    if abs(x2_1 + 1 - x1_2) <= 1:  # room1在左，room2在右
        door_x = x2_1 + 1 if x2_1 + 1 == x1_2 else (x2_1 + x1_2) // 2
        y_overlap_start = max(y1_1, y1_2)
        y_overlap_end = min(y2_1, y2_2)

        if y_overlap_end >= y_overlap_start:
            # 避免在角落放门
            if y_overlap_end > y_overlap_start:
                door_y = random.randint(y_overlap_start, y_overlap_end)
            else:
                door_y = y_overlap_start

            if 0 <= door_x < grid.shape[1] and 0 <= door_y < grid.shape[0]:
                return (door_x, door_y, 101)  # 东西向门

    elif abs(x2_2 + 1 - x1_1) <= 1:  # room2在左，room1在右
        door_x = x2_2 + 1 if x2_2 + 1 == x1_1 else (x2_2 + x1_1) // 2
        y_overlap_start = max(y1_1, y1_2)
        y_overlap_end = min(y2_1, y2_2)

        if y_overlap_end >= y_overlap_start:
            if y_overlap_end > y_overlap_start:
                door_y = random.randint(y_overlap_start, y_overlap_end)
            else:
                door_y = y_overlap_start

            if 0 <= door_x < grid.shape[1] and 0 <= door_y < grid.shape[0]:
                return (door_x, door_y, 101)  # 东西向门

    # 检查垂直相邻（南北向门）
    if abs(y2_1 + 1 - y1_2) <= 1:  # room1在上，room2在下
        door_y = y2_1 + 1 if y2_1 + 1 == y1_2 else (y2_1 + y1_2) // 2
        x_overlap_start = max(x1_1, x1_2)
        x_overlap_end = min(x2_1, x2_2)

        if x_overlap_end >= x_overlap_start:
            if x_overlap_end > x_overlap_start:
                door_x = random.randint(x_overlap_start, x_overlap_end)
            else:
                door_x = x_overlap_start

            if 0 <= door_x < grid.shape[1] and 0 <= door_y < grid.shape[0]:
                return (door_x, door_y, 100)  # 南北向门

    elif abs(y2_2 + 1 - y1_1) <= 1:  # room2在上，room1在下
        door_y = y2_2 + 1 if y2_2 + 1 == y1_1 else (y2_2 + y1_1) // 2
        x_overlap_start = max(x1_1, x1_2)
        x_overlap_end = min(x2_1, x2_2)

        if x_overlap_end >= x_overlap_start:
            if x_overlap_end > x_overlap_start:
                door_x = random.randint(x_overlap_start, x_overlap_end)
            else:
                door_x = x_overlap_start

            if 0 <= door_x < grid.shape[1] and 0 <= door_y < grid.shape[0]:
                return (door_x, door_y, 100)  # 南北向门

    return None

def _verify_connectivity(grid: np.ndarray, num_rooms: int) -> bool:
    """验证所有房间是否连通"""
    if num_rooms <= 1:
        return True

    # 找到所有房间的位置
    room_positions = {}
    for i in range(grid.shape[0]):
        for j in range(grid.shape[1]):
            if 1 <= grid[i, j] <= num_rooms:
                room_id = grid[i, j]
                if room_id not in room_positions:
                    room_positions[room_id] = []
                room_positions[room_id].append((i, j))

    # 检查是否所有房间都存在
    if len(room_positions) != num_rooms:
        return False

    # 使用BFS检查从房间1开始能否到达所有其他房间
    visited_rooms = set()
    start_pos = room_positions[1][0]  # 从房间1的第一个位置开始
    queue = [start_pos]
    visited_positions = set([start_pos])

    directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 上下左右

    while queue:
        y, x = queue.pop(0)
        current_value = grid[y, x]

        # 如果当前位置是房间，记录访问过的房间
        if 1 <= current_value <= num_rooms:
            visited_rooms.add(current_value)

        # 探索相邻位置
        for dy, dx in directions:
            ny, nx = y + dy, x + dx

            # 检查边界
            if (0 <= ny < grid.shape[0] and 0 <= nx < grid.shape[1] and
                (ny, nx) not in visited_positions):

                next_value = grid[ny, nx]

                # 可以通过房间或门
                if (1 <= next_value <= num_rooms) or next_value == 100 or next_value == 101:
                    visited_positions.add((ny, nx))
                    queue.append((ny, nx))

    # 检查是否访问了所有房间
    return len(visited_rooms) == num_rooms

def _verify_no_rooms_at_boundary(grid: np.ndarray, num_rooms: int) -> bool:
    """验证没有房间到达网格边界"""
    n = grid.shape[0]

    # 检查四条边界
    for i in range(n):
        # 上边界和下边界
        if 1 <= grid[0, i] <= num_rooms or 1 <= grid[n-1, i] <= num_rooms:
            return False
        # 左边界和右边界
        if 1 <= grid[i, 0] <= num_rooms or 1 <= grid[i, n-1] <= num_rooms:
            return False

    return True

def _grid_to_emoji(grid: np.ndarray) -> str:
    """将网格转换为emoji显示"""
    emoji_map = {
        -1: "⬛",  # 不可通行区域 - 黑色方块
        0: "🧱",   # 墙壁 - 砖块
        100: "🚪", # 南北向门 - 门
        101: "🚪", # 东西向门 - 门
    }

    # 房间使用不同颜色的方块
    room_emojis = ["🟦", "🟩", "🟨", "🟪", "🟧", "🟫", "⬜", "🟥"]

    result = []
    for row in grid:
        emoji_row = []
        for val in row:
            if val == -1:
                emoji_row.append("⬛")
            elif val == 0:
                emoji_row.append("🧱")
            elif val == 100 or val == 101:
                emoji_row.append("🚪")
            elif 1 <= val <= len(room_emojis):
                emoji_row.append(room_emojis[val - 1])
            else:
                emoji_row.append("❓")  # 未知值
        result.append("".join(emoji_row))

    return "\n".join(result)

# 测试函数
if __name__ == "__main__":
    for level in range(5):  # 只测试前3个level
        # 使用不同的随机种子来展示房间大小的多样性
        seed = 42 + level * 10
        # 为level > 0的情况添加main参数测试
        main_size =  10 if level > 0 else None
        grid = generate_rooms(20, level, main=main_size, seed=seed)
        if main_size:
            print(f"🎯 指定主房间大小: {main_size}×{main_size}")
        unique_values = sorted(set(grid.flatten()))
        print(f"📊 网格中的唯一值: {unique_values}")

        # 统计房间数量
        room_count = sum(1 for v in unique_values if v > 0 and v < 100)
        door_count = sum(1 for v in unique_values if v >= 100)
        print(f"🏠 实际房间数: {room_count}, 🚪 门数: {door_count}")

        # 打印数字版本
        print("🔢 数字版本:")
        with np.printoptions(linewidth=np.inf, threshold=np.inf):
            for row in grid:
                print(' '.join(f'{val:3d}' for val in row))

        # 打印emoji版本
        print("🎨 Emoji版本:")
        print(_grid_to_emoji(grid))
