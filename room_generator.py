import numpy as np
import random
from typing import List, Tuple, Set

def generate_rooms(n: int, level: int) -> np.ndarray:
    """
    生成复杂度为level的房间布局
    
    Args:
        n: 空间大小，生成n*n的数组
        level: 复杂度等级，0表示1个房间，1表示2个房间，以此类推
        
    Returns:
        n*n的numpy数组，其中：
        - 1到level+1: 房间ID
        - 0: 墙
        - -1: 不可通行区域（房间外）
        - 100: 南北向门
        - 101: 东西向门
    """
    # 初始化数组，全部设为不可通行区域
    grid = np.full((n, n), -1, dtype=int)
    
    # 房间数量
    num_rooms = level + 1
    
    # 如果只有一个房间，直接生成一个大房间
    if num_rooms == 1:
        return _generate_single_room(grid, n)
    
    # 生成多个房间
    rooms = _generate_multiple_rooms(grid, n, num_rooms)
    
    # 连接房间（生成门）
    _connect_rooms(grid, rooms)
    
    return grid

def _generate_single_room(grid: np.ndarray, n: int) -> np.ndarray:
    """生成单个房间"""
    # 留出边界作为墙
    margin = max(1, n // 10)
    
    # 房间区域
    start_row = margin
    end_row = n - margin - 1
    start_col = margin
    end_col = n - margin - 1
    
    # 填充房间内部
    grid[start_row+1:end_row, start_col+1:end_col] = 1
    
    # 填充房间边界（墙）
    grid[start_row:end_row+1, start_col] = 0  # 左墙
    grid[start_row:end_row+1, end_col] = 0    # 右墙
    grid[start_row, start_col:end_col+1] = 0  # 上墙
    grid[end_row, start_col:end_col+1] = 0    # 下墙
    
    return grid

def _generate_multiple_rooms(grid: np.ndarray, n: int, num_rooms: int) -> List[Tuple[int, int, int, int]]:
    """生成多个房间，返回房间边界列表"""
    rooms = []
    
    # 计算合适的房间大小
    min_room_size = max(3, n // (num_rooms + 2))
    max_room_size = max(min_room_size + 2, n // 2)
    
    # 尝试放置房间
    attempts = 0
    max_attempts = 1000
    
    while len(rooms) < num_rooms and attempts < max_attempts:
        attempts += 1
        
        # 随机生成房间大小
        room_width = random.randint(min_room_size, max_room_size)
        room_height = random.randint(min_room_size, max_room_size)
        
        # 随机生成房间位置
        start_row = random.randint(1, n - room_height - 2)
        start_col = random.randint(1, n - room_width - 2)
        end_row = start_row + room_height
        end_col = start_col + room_width
        
        # 检查是否与现有房间重叠
        if _check_room_overlap(rooms, start_row, start_col, end_row, end_col):
            continue
            
        # 添加房间
        rooms.append((start_row, start_col, end_row, end_col))
        room_id = len(rooms)
        
        # 在网格中绘制房间
        _draw_room(grid, start_row, start_col, end_row, end_col, room_id)
    
    # 如果无法生成足够的房间，使用分割方法
    if len(rooms) < num_rooms:
        rooms = _generate_rooms_by_division(grid, n, num_rooms)
    
    return rooms

def _check_room_overlap(rooms: List[Tuple[int, int, int, int]], 
                       start_row: int, start_col: int, 
                       end_row: int, end_col: int) -> bool:
    """检查房间是否与现有房间重叠"""
    for r_start_row, r_start_col, r_end_row, r_end_col in rooms:
        # 检查是否有重叠（包括墙的空间）
        if not (end_row + 1 < r_start_row or start_row > r_end_row + 1 or
                end_col + 1 < r_start_col or start_col > r_end_col + 1):
            return True
    return False

def _draw_room(grid: np.ndarray, start_row: int, start_col: int, 
               end_row: int, end_col: int, room_id: int):
    """在网格中绘制房间"""
    # 填充房间内部
    grid[start_row+1:end_row, start_col+1:end_col] = room_id
    
    # 绘制墙
    grid[start_row:end_row+1, start_col] = 0      # 左墙
    grid[start_row:end_row+1, end_col] = 0        # 右墙
    grid[start_row, start_col:end_col+1] = 0      # 上墙
    grid[end_row, start_col:end_col+1] = 0        # 下墙

def _generate_rooms_by_division(grid: np.ndarray, n: int, num_rooms: int) -> List[Tuple[int, int, int, int]]:
    """通过递归分割生成房间"""
    rooms = []
    
    # 从整个可用空间开始
    margin = 1
    _divide_space(grid, margin, margin, n-margin-1, n-margin-1, num_rooms, rooms, 1)
    
    return rooms

def _divide_space(grid: np.ndarray, start_row: int, start_col: int, 
                  end_row: int, end_col: int, remaining_rooms: int, 
                  rooms: List[Tuple[int, int, int, int]], next_room_id: int):
    """递归分割空间"""
    if remaining_rooms <= 1:
        # 创建最后一个房间
        rooms.append((start_row, start_col, end_row, end_col))
        _draw_room(grid, start_row, start_col, end_row, end_col, next_room_id)
        return
    
    width = end_col - start_col
    height = end_row - start_row
    
    # 决定水平还是垂直分割
    if width > height:
        # 垂直分割
        split_col = start_col + width // 2
        rooms_left = remaining_rooms // 2
        rooms_right = remaining_rooms - rooms_left
        
        _divide_space(grid, start_row, start_col, end_row, split_col, 
                     rooms_left, rooms, next_room_id)
        _divide_space(grid, start_row, split_col, end_row, end_col, 
                     rooms_right, rooms, next_room_id + rooms_left)
    else:
        # 水平分割
        split_row = start_row + height // 2
        rooms_top = remaining_rooms // 2
        rooms_bottom = remaining_rooms - rooms_top
        
        _divide_space(grid, start_row, start_col, split_row, end_col, 
                     rooms_top, rooms, next_room_id)
        _divide_space(grid, split_row, start_col, end_row, end_col, 
                     rooms_bottom, rooms, next_room_id + rooms_top)

def _connect_rooms(grid: np.ndarray, rooms: List[Tuple[int, int, int, int]]):
    """连接房间，确保形成连通图但无环"""
    if len(rooms) <= 1:
        return
    
    # 使用最小生成树算法连接房间
    connected = {0}  # 已连接的房间集合
    unconnected = set(range(1, len(rooms)))  # 未连接的房间集合
    
    while unconnected:
        best_connection = None
        min_distance = float('inf')
        
        # 找到最近的连接
        for connected_idx in connected:
            for unconnected_idx in unconnected:
                distance = _calculate_room_distance(rooms[connected_idx], rooms[unconnected_idx])
                if distance < min_distance:
                    min_distance = distance
                    best_connection = (connected_idx, unconnected_idx)
        
        if best_connection:
            room1_idx, room2_idx = best_connection
            _create_door(grid, rooms[room1_idx], rooms[room2_idx])
            connected.add(room2_idx)
            unconnected.remove(room2_idx)

def _calculate_room_distance(room1: Tuple[int, int, int, int], 
                           room2: Tuple[int, int, int, int]) -> float:
    """计算两个房间之间的距离"""
    r1_center_row = (room1[0] + room1[2]) / 2
    r1_center_col = (room1[1] + room1[3]) / 2
    r2_center_row = (room2[0] + room2[2]) / 2
    r2_center_col = (room2[1] + room2[3]) / 2
    
    return ((r1_center_row - r2_center_row) ** 2 + (r1_center_col - r2_center_col) ** 2) ** 0.5

def _create_door(grid: np.ndarray, room1: Tuple[int, int, int, int], 
                room2: Tuple[int, int, int, int]):
    """在两个房间之间创建门"""
    r1_start_row, r1_start_col, r1_end_row, r1_end_col = room1
    r2_start_row, r2_start_col, r2_end_row, r2_end_col = room2
    
    # 找到最佳门的位置
    door_pos = _find_best_door_position(grid, room1, room2)
    
    if door_pos:
        row, col, is_horizontal = door_pos
        if is_horizontal:
            grid[row, col] = 101  # 东西向门
        else:
            grid[row, col] = 100  # 南北向门

def _find_best_door_position(grid: np.ndarray, room1: Tuple[int, int, int, int], 
                           room2: Tuple[int, int, int, int]) -> Tuple[int, int, bool]:
    """找到两个房间之间最佳的门位置"""
    r1_start_row, r1_start_col, r1_end_row, r1_end_col = room1
    r2_start_row, r2_start_col, r2_end_row, r2_end_col = room2
    
    # 检查水平相邻
    if (r1_end_col == r2_start_col - 1 or r2_end_col == r1_start_col - 1):
        # 找到重叠的行范围
        overlap_start = max(r1_start_row + 1, r2_start_row + 1)
        overlap_end = min(r1_end_row - 1, r2_end_row - 1)
        
        if overlap_start <= overlap_end:
            door_row = (overlap_start + overlap_end) // 2
            if r1_end_col == r2_start_col - 1:
                door_col = r1_end_col
            else:
                door_col = r2_end_col
            return (door_row, door_col, True)  # 东西向门
    
    # 检查垂直相邻
    if (r1_end_row == r2_start_row - 1 or r2_end_row == r1_start_row - 1):
        # 找到重叠的列范围
        overlap_start = max(r1_start_col + 1, r2_start_col + 1)
        overlap_end = min(r1_end_col - 1, r2_end_col - 1)
        
        if overlap_start <= overlap_end:
            door_col = (overlap_start + overlap_end) // 2
            if r1_end_row == r2_start_row - 1:
                door_row = r1_end_row
            else:
                door_row = r2_end_row
            return (door_row, door_col, False)  # 南北向门
    
    return None

# 示例使用
if __name__ == "__main__":
    # 测试不同复杂度的房间生成
    for level in range(4):
        print(f"\n=== Level {level} (房间数: {level + 1}) ===")
        result = generate_rooms(20, level)
        print(result)
        print(f"房间ID范围: {np.min(result[result > 0])} - {np.max(result[result > 0])}")
        print(f"门的数量: {np.sum((result == 100) | (result == 101))}")
